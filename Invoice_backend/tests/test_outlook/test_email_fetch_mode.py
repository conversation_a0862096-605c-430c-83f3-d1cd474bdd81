"""
Test cases for email fetch mode functionality
"""

import pytest
from datetime import datetime, UTC
from unittest.mock import AsyncMock, MagicMock, patch
import json

from app.schemas.outlook import OutlookConnectionCreate, EmailFetchRequest
from app.services.outlook_service_admin import AdminOutlookService
from app.services.email_monitor_service import EmailMonitorService


class TestEmailFetchMode:
    """Test email fetch mode functionality"""

    @pytest.fixture
    def mock_database_ops(self):
        """Mock database operations"""
        mock_db = AsyncMock()
        return mock_db

    @pytest.fixture
    def mock_graph_client(self):
        """Mock Microsoft Graph client"""
        mock_client = AsyncMock()
        mock_client.get_authorization_url.return_value = ("https://login.microsoftonline.com/oauth", "test_state")
        return mock_client

    @pytest.fixture
    def admin_outlook_service(self, mock_database_ops, mock_graph_client):
        """Create AdminOutlookService with mocked dependencies"""
        with patch('app.services.outlook_service_admin.AdminOutlookDatabaseOps') as mock_db_ops_class:
            mock_db_ops_class.return_value = mock_database_ops
            with patch('app.services.outlook_service_admin.MicrosoftGraphClient') as mock_graph_class:
                mock_graph_class.return_value = mock_graph_client
                service = AdminOutlookService(None)  # db will be mocked
                return service

    @pytest.mark.asyncio
    async def test_get_authorization_url_with_new_only_mode(self, admin_outlook_service):
        """Test authorization URL generation with new_only email fetch mode"""
        admin_email = "<EMAIL>"
        email_fetch_mode = "new_only"

        result = await admin_outlook_service.get_authorization_url(admin_email, email_fetch_mode)

        assert result.success is True
        assert result.auth_url == "https://login.microsoftonline.com/oauth"
        
        # Verify that the state parameter contains the email fetch mode
        admin_outlook_service.graph_client.get_authorization_url.assert_called_once()
        call_args = admin_outlook_service.graph_client.get_authorization_url.call_args[0][0]
        state_data = json.loads(call_args)
        
        assert state_data["admin_email"] == admin_email
        assert state_data["email_fetch_mode"] == email_fetch_mode
        assert "timestamp" in state_data

    @pytest.mark.asyncio
    async def test_get_authorization_url_with_all_emails_mode(self, admin_outlook_service):
        """Test authorization URL generation with all_emails email fetch mode"""
        admin_email = "<EMAIL>"
        email_fetch_mode = "all_emails"

        result = await admin_outlook_service.get_authorization_url(admin_email, email_fetch_mode)

        assert result.success is True
        
        # Verify that the state parameter contains the email fetch mode
        call_args = admin_outlook_service.graph_client.get_authorization_url.call_args[0][0]
        state_data = json.loads(call_args)
        
        assert state_data["email_fetch_mode"] == email_fetch_mode

    @pytest.mark.asyncio
    async def test_handle_oauth_callback_extracts_email_fetch_mode(self, admin_outlook_service):
        """Test that OAuth callback extracts email fetch mode from state"""
        admin_email = "<EMAIL>"
        authorization_code = "test_code"
        
        # Mock state parameter with email fetch mode
        state_data = {
            "admin_email": admin_email,
            "email_fetch_mode": "new_only",
            "timestamp": datetime.now(UTC).isoformat()
        }
        state = json.dumps(state_data)

        # Mock token exchange and user profile
        admin_outlook_service.graph_client.exchange_code_for_tokens.return_value = {
            "success": True,
            "access_token": "test_token",
            "refresh_token": "test_refresh",
            "expires_at": datetime.now(UTC)
        }
        
        admin_outlook_service.graph_client.get_user_profile.return_value = {
            "success": True,
            "user_data": {"mail": "<EMAIL>"}
        }
        
        admin_outlook_service.database_ops.create_connection.return_value = {
            "success": True,
            "connection_id": "test_connection_id"
        }

        result = await admin_outlook_service.handle_oauth_callback(admin_email, authorization_code, state)

        # Verify that create_connection was called with the correct email_fetch_mode
        admin_outlook_service.database_ops.create_connection.assert_called_once()
        connection_data = admin_outlook_service.database_ops.create_connection.call_args[0][1]
        
        assert connection_data.email_fetch_mode == "new_only"

    def test_outlook_connection_create_schema_with_email_fetch_mode(self):
        """Test OutlookConnectionCreate schema includes email_fetch_mode"""
        connection_data = OutlookConnectionCreate(
            user_email="<EMAIL>",
            access_token="test_token",
            refresh_token="test_refresh",
            token_expires_at=datetime.now(UTC),
            scopes=["Mail.Read"],
            email_fetch_mode="all_emails"
        )
        
        assert connection_data.email_fetch_mode == "all_emails"

    def test_email_fetch_request_schema_with_date_filter(self):
        """Test EmailFetchRequest schema includes date_filter"""
        request = EmailFetchRequest(
            user_email="<EMAIL>",
            limit=50,
            skip=0,
            folder="inbox",
            date_filter="2024-01-01T00:00:00Z"
        )
        
        assert request.date_filter == "2024-01-01T00:00:00Z"

    @pytest.mark.asyncio
    async def test_email_monitor_service_respects_new_only_mode(self):
        """Test that EmailMonitorService respects new_only mode on first sync"""
        # Mock connection with new_only mode and no initial sync completed
        connection = {
            "_id": "test_connection_id",
            "user_email": "<EMAIL>",
            "email_fetch_mode": "new_only",
            "initial_sync_completed": False,
            "created_at": datetime.now(UTC)
        }

        # Mock outlook service
        mock_outlook_service = AsyncMock()
        mock_fetch_result = MagicMock()
        mock_fetch_result.success = True
        mock_fetch_result.new_emails = 5
        mock_outlook_service.fetch_emails.return_value = mock_fetch_result

        # Create email monitor service
        monitor_service = EmailMonitorService(mock_outlook_service)
        
        # Mock the database operations
        with patch('app.services.email_monitor_service.get_database') as mock_get_db:
            mock_db = AsyncMock()
            mock_get_db.return_value = mock_db
            
            # Mock the _get_last_delta_link method
            monitor_service._get_last_delta_link = AsyncMock(return_value=None)
            
            # Call the sync method
            await monitor_service._sync_connection_emails(connection)

        # Verify that fetch_emails was called with a date filter
        mock_outlook_service.fetch_emails.assert_called_once()
        call_args = mock_outlook_service.fetch_emails.call_args
        request = call_args[1]['request']  # keyword argument
        
        assert request.date_filter is not None
        assert "T" in request.date_filter  # Should be ISO format datetime

    @pytest.mark.asyncio
    async def test_email_monitor_service_respects_all_emails_mode(self):
        """Test that EmailMonitorService respects all_emails mode"""
        # Mock connection with all_emails mode - now includes the required fields
        connection = {
            "_id": "test_connection_id",
            "user_email": "<EMAIL>",
            "email_fetch_mode": "all_emails",  # This field is now properly retrieved
            "initial_sync_completed": False,   # This field is now properly retrieved
            "created_at": datetime.now(UTC)
        }

        # Mock outlook service
        mock_outlook_service = AsyncMock()
        mock_fetch_result = MagicMock()
        mock_fetch_result.success = True
        mock_fetch_result.new_emails = 10
        mock_outlook_service.fetch_emails.return_value = mock_fetch_result

        # Create email monitor service
        monitor_service = EmailMonitorService(mock_outlook_service)

        # Mock the database operations
        with patch('app.services.email_monitor_service.get_database') as mock_get_db:
            mock_db = AsyncMock()
            mock_get_db.return_value = mock_db

            # Mock the _get_last_delta_link method
            monitor_service._get_last_delta_link = AsyncMock(return_value=None)

            # Call the sync method
            await monitor_service._sync_connection_emails(connection)

        # Verify that fetch_emails was called without a date filter
        mock_outlook_service.fetch_emails.assert_called_once()
        call_args = mock_outlook_service.fetch_emails.call_args
        request = call_args[1]['request']  # keyword argument

        assert request.date_filter is None  # No date filter for all_emails mode - this should now work correctly!
