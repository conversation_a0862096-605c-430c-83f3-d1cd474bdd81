"""
Admin-based Outlook database operations service for managing connections and email tracking
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, UTC, timedelta
from bson import ObjectId
from motor.motor_asyncio import AsyncIOMotorDatabase

from app.core.config import settings
from app.schemas.outlook import (
    OutlookConnectionCreate, OutlookConnectionUpdate, OutlookConnectionInDB,
    EmailTrackingInDB, EmailMetadata
)
from app.services.token_manager import token_manager

logger = logging.getLogger(__name__)


class AdminOutlookDatabase:
    """Admin-based database operations for Outlook integration"""
    
    def __init__(self, database: AsyncIOMotorDatabase):
        """Initialize with database connection"""
        self.db = database
        self.admin_collection = self.db["admin"]
        self.email_tracking_collection = self.db[settings.EMAIL_TRACKING_COLLECTION]
    
    async def create_connection(
        self, 
        admin_email: str, 
        connection_data: OutlookConnectionCreate
    ) -> Dict[str, Any]:
        """
        Create new Outlook connection for admin
        
        Args:
            admin_email: Admin email address
            connection_data: Connection data
            
        Returns:
            Result with success status and connection ID if successful
        """
        try:
            # Check if connection already exists for this email under this admin
            admin_doc = await self.admin_collection.find_one({
                "admin_email": admin_email,
                "outlook_connections.user_email": connection_data.user_email.lower().strip()
            })
            
            if admin_doc:
                # Find the existing connection
                for conn in admin_doc.get("outlook_connections", []):
                    if conn["user_email"] == connection_data.user_email.lower().strip():
                        logger.info(f"Connection already exists for {connection_data.user_email}")
                        return {
                            "success": False,
                            "message": f"Connection already exists for {connection_data.user_email}",
                            "connection_id": str(conn["connection_id"])
                        }
            
            # Encrypt sensitive data
            access_token_encrypted = token_manager.encryption.encrypt_token(
                connection_data.access_token
            )
            refresh_token_encrypted = token_manager.encryption.encrypt_token(
                connection_data.refresh_token
            )
            
            # Create connection document
            connection_id = ObjectId()
            now = datetime.now(UTC)
            connection = {
                "connection_id": connection_id,
                "user_email": connection_data.user_email.lower().strip(),
                "tenant_id": getattr(connection_data, 'tenant_id', None),
                "access_token_encrypted": access_token_encrypted,
                "refresh_token_encrypted": refresh_token_encrypted,
                "token_expires_at": connection_data.token_expires_at,
                "scopes": connection_data.scopes,
                "is_active": True,
                "created_at": now,
                "updated_at": now,
                "last_sync_at": None,
                "delta_token": None,
                "email_fetch_mode": getattr(connection_data, 'email_fetch_mode', 'new_only'),
                "initial_sync_completed": False
            }
            
            # Add connection to admin document
            result = await self.admin_collection.update_one(
                {"admin_email": admin_email},
                {
                    "$push": {"outlook_connections": connection},
                    "$set": {"updated_at": now}
                }
            )
            
            if result.modified_count > 0:
                logger.info(f"Created new connection for {connection_data.user_email} under admin {admin_email}")
                return {
                    "success": True,
                    "connection_id": str(connection_id)
                }
            else:
                logger.error(f"Failed to create connection for admin: {admin_email}")
                return {
                    "success": False,
                    "error": "database_error",
                    "message": "Failed to add connection to admin account"
                }
            
        except Exception as e:
            logger.error(f"Error creating connection: {str(e)}")
            return {
                "success": False,
                "error": "database_error",
                "message": str(e)
            }
    
    async def get_connection(
        self, 
        admin_email: str, 
        connection_id: str
    ) -> Optional[OutlookConnectionInDB]:
        """
        Get Outlook connection by ID for admin
        
        Args:
            admin_email: Admin email address
            connection_id: Connection ID
            
        Returns:
            Connection data or None if not found
        """
        try:
            if not ObjectId.is_valid(connection_id):
                return None
                
            admin_doc = await self.admin_collection.find_one(
                {
                    "admin_email": admin_email,
                    "outlook_connections.connection_id": ObjectId(connection_id)
                },
                {"outlook_connections.$": 1}
            )
            
            if admin_doc and "outlook_connections" in admin_doc:
                connection = admin_doc["outlook_connections"][0]
                # Convert to OutlookConnectionInDB format
                connection_data = {
                    "_id": connection["connection_id"],
                    "user_email": connection["user_email"],
                    "tenant_id": connection.get("tenant_id"),
                    "access_token_encrypted": connection["access_token_encrypted"],
                    "refresh_token_encrypted": connection["refresh_token_encrypted"],
                    "token_expires_at": connection["token_expires_at"],
                    "scopes": connection.get("scopes", []),
                    "is_active": connection.get("is_active", True),
                    "created_at": connection["created_at"],
                    "updated_at": connection["updated_at"],
                    "last_sync_at": connection.get("last_sync_at"),
                    "delta_token": connection.get("delta_token"),
                    "email_fetch_mode": connection.get("email_fetch_mode", "new_only"),
                    "initial_sync_completed": connection.get("initial_sync_completed", False)
                }
                return OutlookConnectionInDB(**connection_data)
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting connection {connection_id}: {str(e)}")
            return None
    
    async def get_connection_by_email(
        self, 
        admin_email: str, 
        user_email: str
    ) -> Optional[OutlookConnectionInDB]:
        """
        Get Outlook connection by user email for admin
        
        Args:
            admin_email: Admin email address
            user_email: User's email address
            
        Returns:
            Connection data or None if not found
        """
        try:
            admin_doc = await self.admin_collection.find_one(
                {
                    "admin_email": admin_email,
                    "outlook_connections.user_email": user_email.lower().strip()
                },
                {"outlook_connections.$": 1}
            )
            
            if admin_doc and "outlook_connections" in admin_doc:
                connection = admin_doc["outlook_connections"][0]
                # Convert to OutlookConnectionInDB format
                connection_data = {
                    "_id": connection["connection_id"],
                    "user_email": connection["user_email"],
                    "tenant_id": connection.get("tenant_id"),
                    "access_token_encrypted": connection["access_token_encrypted"],
                    "refresh_token_encrypted": connection["refresh_token_encrypted"],
                    "token_expires_at": connection["token_expires_at"],
                    "scopes": connection.get("scopes", []),
                    "is_active": connection.get("is_active", True),
                    "created_at": connection["created_at"],
                    "updated_at": connection["updated_at"],
                    "last_sync_at": connection.get("last_sync_at"),
                    "delta_token": connection.get("delta_token"),
                    "email_fetch_mode": connection.get("email_fetch_mode", "new_only"),
                    "initial_sync_completed": connection.get("initial_sync_completed", False)
                }
                return OutlookConnectionInDB(**connection_data)
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting connection for email {user_email}: {str(e)}")
            return None
    
    async def update_connection(
        self,
        admin_email: str,
        connection_id: str,
        update_data: OutlookConnectionUpdate
    ) -> bool:
        """
        Update Outlook connection for admin
        
        Args:
            admin_email: Admin email address
            connection_id: Connection ID
            update_data: Update data
            
        Returns:
            True if updated successfully, False otherwise
        """
        try:
            if not ObjectId.is_valid(connection_id):
                return False
            
            update_fields = {}
            
            # Handle token updates with encryption
            if update_data.access_token:
                update_fields["outlook_connections.$.access_token_encrypted"] = (
                    token_manager.encryption.encrypt_token(update_data.access_token)
                )

            if update_data.refresh_token:
                update_fields["outlook_connections.$.refresh_token_encrypted"] = (
                    token_manager.encryption.encrypt_token(update_data.refresh_token)
                )
            
            if update_data.token_expires_at:
                update_fields["outlook_connections.$.token_expires_at"] = update_data.token_expires_at
            
            if update_data.scopes is not None:
                update_fields["outlook_connections.$.scopes"] = update_data.scopes
            
            if update_data.is_active is not None:
                update_fields["outlook_connections.$.is_active"] = update_data.is_active
            
            # Always update timestamps
            update_fields["outlook_connections.$.updated_at"] = datetime.now(UTC)
            update_fields["updated_at"] = datetime.now(UTC)
            
            result = await self.admin_collection.update_one(
                {
                    "admin_email": admin_email,
                    "outlook_connections.connection_id": ObjectId(connection_id)
                },
                {"$set": update_fields}
            )
            
            success = result.modified_count > 0
            if success:
                logger.info(f"Updated connection {connection_id} for admin {admin_email}")
            
            return success

        except Exception as e:
            logger.error(f"Error updating connection {connection_id}: {str(e)}")
            return False

    async def update_last_sync(self, admin_email: str, connection_id: str) -> bool:
        """
        Update last sync timestamp for connection

        Args:
            admin_email: Admin email address
            connection_id: Connection ID

        Returns:
            True if updated successfully, False otherwise
        """
        try:
            if not ObjectId.is_valid(connection_id):
                return False

            result = await self.admin_collection.update_one(
                {
                    "admin_email": admin_email,
                    "outlook_connections.connection_id": ObjectId(connection_id)
                },
                {
                    "$set": {
                        "outlook_connections.$.last_sync_at": datetime.now(UTC),
                        "outlook_connections.$.updated_at": datetime.now(UTC),
                        "updated_at": datetime.now(UTC)
                    }
                }
            )

            return result.modified_count > 0

        except Exception as e:
            logger.error(f"Error updating last sync for {connection_id}: {str(e)}")
            return False

    async def update_last_sync_by_connection_id(self, connection_id: str) -> bool:
        """
        Update last sync timestamp for connection by finding the admin automatically

        Args:
            connection_id: Connection ID

        Returns:
            True if updated successfully, False otherwise
        """
        try:
            from bson import ObjectId

            # Convert string to ObjectId if needed
            try:
                connection_obj_id = ObjectId(connection_id)
            except:
                connection_obj_id = connection_id

            # Find the admin document that contains this connection
            result = await self.admin_collection.update_one(
                {
                    "$or": [
                        {"outlook_connections.connection_id": connection_obj_id},
                        {"outlook_connections.connection_id": connection_id}
                    ]
                },
                {
                    "$set": {
                        "outlook_connections.$.last_sync_at": datetime.now(UTC),
                        "outlook_connections.$.updated_at": datetime.now(UTC),
                        "updated_at": datetime.now(UTC)
                    }
                }
            )

            return result.modified_count > 0

        except Exception as e:
            logger.error(f"Error updating last sync for connection {connection_id}: {str(e)}")
            return False

    async def list_connections(
        self,
        admin_email: str,
        skip: int = 0,
        limit: int = 50,
        active_only: bool = True
    ) -> Dict[str, Any]:
        """
        List Outlook connections for admin with pagination

        Args:
            admin_email: Admin email address
            skip: Number of connections to skip
            limit: Maximum number of connections to return
            active_only: Only return active connections

        Returns:
            List of connections with metadata
        """
        try:
            # Get admin document
            admin_doc = await self.admin_collection.find_one({"admin_email": admin_email})

            if not admin_doc:
                return {
                    "success": False,
                    "error": "admin_not_found",
                    "message": f"Admin not found: {admin_email}"
                }

            # Get connections
            all_connections = admin_doc.get("outlook_connections", [])

            # Filter active connections if requested
            if active_only:
                all_connections = [conn for conn in all_connections if conn.get("is_active", True)]

            # Apply pagination
            total = len(all_connections)
            connections = all_connections[skip:skip + limit]

            # Format connections for response
            formatted_connections = []
            for connection in connections:
                connection_data = {
                    "id": str(connection["connection_id"]),
                    "user_email": connection["user_email"],
                    "token_expires_at": connection["token_expires_at"],
                    "scopes": connection.get("scopes", []),
                    "created_at": connection["created_at"],
                    "updated_at": connection["updated_at"],
                    "last_sync_at": connection.get("last_sync_at"),
                    "is_active": connection.get("is_active", True)
                }
                formatted_connections.append(connection_data)

            return {
                "success": True,
                "connections": formatted_connections,
                "total": total,
                "skip": skip,
                "limit": limit
            }

        except Exception as e:
            logger.error(f"Error listing connections for admin {admin_email}: {str(e)}")
            return {
                "success": False,
                "error": "database_error",
                "message": str(e)
            }

    async def delete_connection(self, admin_email: str, connection_id: str) -> bool:
        """
        Delete Outlook connection for admin

        Args:
            admin_email: Admin email address
            connection_id: Connection ID

        Returns:
            True if deleted successfully, False otherwise
        """
        try:
            if not ObjectId.is_valid(connection_id):
                return False

            result = await self.admin_collection.update_one(
                {"admin_email": admin_email},
                {
                    "$pull": {"outlook_connections": {"connection_id": ObjectId(connection_id)}},
                    "$set": {"updated_at": datetime.now(UTC)}
                }
            )

            success = result.modified_count > 0
            if success:
                logger.info(f"Deleted connection {connection_id} for admin {admin_email}")

            return success

        except Exception as e:
            logger.error(f"Error deleting connection {connection_id}: {str(e)}")
            return False

    # Email tracking methods (these remain largely the same as they use a separate collection)

    async def track_email(self, connection_id: str, email_metadata: EmailMetadata) -> bool:
        """
        Track processed email to prevent duplicates

        Args:
            connection_id: Connection ID
            email_metadata: Email metadata

        Returns:
            True if tracked successfully, False otherwise
        """
        try:
            # Check if email is already tracked using internet_message_id for consistency
            existing = await self.email_tracking_collection.find_one({
                "connection_id": ObjectId(connection_id),
                "internet_message_id": email_metadata.internet_message_id
            })

            if existing:
                logger.debug(f"Email already tracked: {email_metadata.internet_message_id}")
                return True

            # Create tracking record
            tracking_doc = EmailTrackingInDB(
                connection_id=ObjectId(connection_id),
                message_id=email_metadata.message_id,
                internet_message_id=email_metadata.internet_message_id,
                subject=email_metadata.subject,
                sender_email=email_metadata.sender_email,
                sender_name=email_metadata.sender_name,
                received_datetime=email_metadata.received_datetime,
                has_attachments=email_metadata.has_attachments,
                importance=email_metadata.importance,
                is_read=email_metadata.is_read,
                folder_name=email_metadata.folder_name,
                processing_status="processed",
                processed_at=datetime.now(UTC),
                created_at=datetime.now(UTC),
                updated_at=datetime.now(UTC)
            )

            await self.email_tracking_collection.insert_one(
                tracking_doc.model_dump(by_alias=True, exclude={"id"})
            )

            return True

        except Exception as e:
            logger.error(f"Error tracking email: {str(e)}")
            return False

    async def track_email_with_content(
        self,
        connection_id: str,
        enhanced_email_data: Dict[str, Any]
    ) -> bool:
        """
        Track email with full content and attachments in admin collection

        Args:
            connection_id: Connection ID
            enhanced_email_data: Enhanced email data with content and attachments

        Returns:
            True if successful, False otherwise
        """
        try:
            # Convert connection_id to ObjectId
            connection_obj_id = ObjectId(connection_id)

            # Check for duplicates first using internet_message_id
            internet_message_id = enhanced_email_data["internet_message_id"]

            # Check if email already exists in admin collection (across all connections for this admin)
            existing_email = await self.admin_collection.find_one({
                "outlook_connections.emails.internet_message_id": internet_message_id
            })

            if existing_email:
                logger.debug(f"Email already exists in admin collection: {internet_message_id}")
                return True

            # Create email document to store in admin collection
            email_doc = {
                "message_id": enhanced_email_data["message_id"],
                "internet_message_id": enhanced_email_data["internet_message_id"],
                "subject": enhanced_email_data.get("subject"),
                "sender_email": enhanced_email_data.get("sender_email"),
                "sender_name": enhanced_email_data.get("sender_name"),
                "received_datetime": enhanced_email_data["received_datetime"],
                "has_attachments": enhanced_email_data.get("has_attachments", False),
                "importance": enhanced_email_data.get("importance", "normal"),
                "is_read": enhanced_email_data.get("is_read", False),
                "folder_name": enhanced_email_data.get("folder_name", "inbox"),

                # Enhanced content fields
                "body_content": enhanced_email_data.get("body_content"),
                "body_content_type": enhanced_email_data.get("body_content_type", "text"),
                "body_preview": enhanced_email_data.get("body_preview"),

                # Attachment information
                "attachments": enhanced_email_data.get("attachments", []),
                "attachment_count": enhanced_email_data.get("attachment_count", 0),

                # Processing information
                "processing_status": enhanced_email_data.get("processing_status", "processed"),
                "processed_at": enhanced_email_data.get("processed_at", datetime.now(UTC)),
                "content_downloaded": enhanced_email_data.get("content_downloaded", False),
                "attachments_downloaded": enhanced_email_data.get("attachments_downloaded", False),
                "created_at": datetime.now(UTC)
            }

            # Add email to the specific connection's emails array in admin collection
            result = await self.admin_collection.update_one(
                {
                    "$or": [
                        {"outlook_connections.connection_id": connection_obj_id},
                        {"outlook_connections.connection_id": connection_id}
                    ]
                },
                {
                    "$push": {
                        "outlook_connections.$.emails": email_doc
                    },
                    "$set": {
                        "outlook_connections.$.updated_at": datetime.now(UTC),
                        "updated_at": datetime.now(UTC)
                    }
                }
            )

            if result.modified_count > 0:
                logger.info(f"Successfully tracked enhanced email in admin collection: {enhanced_email_data['message_id']} with {enhanced_email_data.get('attachment_count', 0)} attachments")
                return True
            else:
                logger.error(f"Failed to add email to admin collection: {enhanced_email_data['message_id']}")
                return False

        except Exception as e:
            logger.error(f"Error tracking enhanced email in admin collection: {str(e)}")
            return False

    async def is_email_processed(self, connection_id: str, internet_message_id: str) -> bool:
        """
        Check if email has been processed by looking in admin collection

        Args:
            connection_id: Connection ID
            internet_message_id: Internet message ID (used for consistency with regular database)

        Returns:
            True if email has been processed, False otherwise
        """
        try:
            # Convert connection_id to ObjectId
            connection_obj_id = ObjectId(connection_id)

            # Check if email exists in admin collection (across all connections for this admin)
            existing_email = await self.admin_collection.find_one({
                "outlook_connections.emails.internet_message_id": internet_message_id
            })

            # Also check in email_tracking collection for backward compatibility
            if not existing_email:
                existing_tracking = await self.email_tracking_collection.find_one({
                    "connection_id": connection_obj_id,
                    "internet_message_id": internet_message_id
                })
                return existing_tracking is not None

            return existing_email is not None

        except Exception as e:
            logger.error(f"Error checking if email processed: {str(e)}")
            return False

    async def get_processed_emails(
        self,
        connection_id: str,
        skip: int = 0,
        limit: int = 50
    ) -> Dict[str, Any]:
        """
        Get processed emails for connection

        Args:
            connection_id: Connection ID
            skip: Number of emails to skip
            limit: Maximum number of emails to return

        Returns:
            List of processed emails with metadata
        """
        try:
            if not ObjectId.is_valid(connection_id):
                return {
                    "success": False,
                    "error": "invalid_connection_id",
                    "message": "Invalid connection ID"
                }

            # Get total count
            total = await self.email_tracking_collection.count_documents({
                "connection_id": ObjectId(connection_id)
            })

            # Get emails
            cursor = self.email_tracking_collection.find({
                "connection_id": ObjectId(connection_id)
            }).sort("received_datetime", -1).skip(skip).limit(limit)

            emails = []
            async for email in cursor:
                email_data = {
                    "id": str(email["_id"]),
                    "message_id": email["message_id"],
                    "internet_message_id": email["internet_message_id"],
                    "subject": email["subject"],
                    "sender_email": email["sender_email"],
                    "sender_name": email.get("sender_name"),
                    "received_datetime": email["received_datetime"],
                    "has_attachments": email["has_attachments"],
                    "importance": email["importance"],
                    "is_read": email["is_read"],
                    "folder_name": email.get("folder_name"),
                    "processing_status": email["processing_status"],
                    "processed_at": email.get("processed_at"),
                    "created_at": email["created_at"]
                }
                emails.append(email_data)

            return {
                "success": True,
                "emails": emails,
                "total": total,
                "skip": skip,
                "limit": limit
            }

        except Exception as e:
            logger.error(f"Error getting processed emails: {str(e)}")
            return {
                "success": False,
                "error": "database_error",
                "message": str(e)
            }

    async def get_connection_by_id(self, connection_id: str) -> Optional[OutlookConnectionInDB]:
        """
        Get a connection by its ID from any admin's connections

        Args:
            connection_id: The connection ID to search for

        Returns:
            OutlookConnectionInDB if found, None otherwise
        """
        try:
            from bson import ObjectId

            # Convert string to ObjectId if needed
            try:
                connection_obj_id = ObjectId(connection_id)
            except:
                # If it's not a valid ObjectId, try as string
                connection_obj_id = connection_id

            # Search across all admin documents for the connection (try both ObjectId and string)
            admin_doc = await self.admin_collection.find_one({
                "$or": [
                    {"outlook_connections.connection_id": connection_obj_id},
                    {"outlook_connections.connection_id": connection_id}
                ]
            })

            if not admin_doc:
                logger.debug(f"No admin document found with connection_id: {connection_id}")
                return None

            # Find the specific connection within the admin's connections
            for connection in admin_doc.get("outlook_connections", []):
                conn_id = connection["connection_id"]
                # Compare both as strings to handle ObjectId vs string comparison
                if str(conn_id) == str(connection_id):
                    connection_data = {
                        "_id": str(connection["connection_id"]),  # Convert to string for consistency
                        "user_email": connection["user_email"],
                        "tenant_id": connection.get("tenant_id"),
                        "access_token_encrypted": connection["access_token_encrypted"],
                        "refresh_token_encrypted": connection["refresh_token_encrypted"],
                        "token_expires_at": connection["token_expires_at"],
                        "scopes": connection.get("scopes", []),
                        "is_active": connection.get("is_active", True),
                        "created_at": connection["created_at"],
                        "updated_at": connection["updated_at"],
                        "last_sync_at": connection.get("last_sync_at"),
                        "delta_token": connection.get("delta_token")
                    }
                    logger.debug(f"Found connection: {connection['user_email']} (ID: {connection_data['_id']})")
                    return OutlookConnectionInDB(**connection_data)

            logger.debug(f"Connection {connection_id} not found in admin document")
            return None

        except Exception as e:
            logger.error(f"Error getting connection by ID {connection_id}: {str(e)}")
            return None
